"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState, useCallback, useRef } from "react";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { SupportedLanguage } from "../../../../hooks/useLanguagePreference";

interface EnhancedVoiceRecorderProps {
  setUserMessage: (message: string) => void;
  language?: SupportedLanguage;
  disabled?: boolean;
  className?: string;
  onTranscriptionStart?: () => void;
  onTranscriptionEnd?: () => void;
}

// Language mapping for speech recognition
const getRecognitionLanguage = (language: SupportedLanguage): string => {
  const languageMap: Record<SupportedLanguage, string> = {
    en: "en-US",
    hi: "hi-IN",
    "hi-en": "hi-IN", // Hinglish uses Hindi recognition
    ta: "ta-IN",
    te: "te-IN",
    bn: "bn-IN",
    mr: "mr-IN",
    gu: "gu-IN",
    es: "es-ES",
    fr: "fr-FR",
    de: "de-DE",
    it: "it-IT",
    pt: "pt-BR",
    ru: "ru-RU",
    ja: "ja-JP",
    ko: "ko-KR",
    zh: "zh-CN",
    ar: "ar-SA",
  };
  return languageMap[language] || "en-US";
};

const EnhancedVoiceRecorder = ({
  setUserMessage,
  language = "en",
  disabled = false,
  className = "",
  onTranscriptionStart,
  onTranscriptionEnd,
}: EnhancedVoiceRecorderProps) => {
  const [error, setError] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [useAITranscription, setUseAITranscription] = useState(false);

  // Audio recording refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // Speech recognition fallback
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = useSpeechRecognition();

  // Handle transcript changes from browser speech recognition
  useEffect(() => {
    if (transcript && transcript.trim() && !useAITranscription) {
      setUserMessage(transcript);
      resetTranscript();
    }
  }, [transcript, setUserMessage, resetTranscript, useAITranscription]);

  // Send audio to backend for AI transcription
  const sendAudioForTranscription = useCallback(
    async (audioBlob: Blob) => {
      try {
        setIsProcessing(true);
        onTranscriptionStart?.();

        const formData = new FormData();
        formData.append("audio", audioBlob, "recording.webm");
        formData.append("language", language);

        const response = await fetch("/api/transcribe-audio", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error("Transcription failed");
        }

        const result = await response.json();

        if (result.success && result.transcript) {
          setUserMessage(result.transcript);
        } else {
          throw new Error(result.error || "No transcript received");
        }
      } catch (error) {
        console.error("AI transcription failed:", error);
        setError("Transcription failed, trying browser fallback...");

        // Fallback to browser speech recognition
        setUseAITranscription(false);
        setTimeout(() => setError(null), 3000);
      } finally {
        setIsProcessing(false);
        onTranscriptionEnd?.();
      }
    },
    [language, setUserMessage, onTranscriptionStart, onTranscriptionEnd]
  );

  // Start audio recording
  const startAudioRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000, // Optimal for speech recognition
        },
      });

      streamRef.current = stream;
      audioChunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm;codecs=opus",
        });

        if (audioBlob.size > 0) {
          sendAudioForTranscription(audioBlob);
        }

        // Cleanup
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.start();
      setIsRecording(true);
      setUseAITranscription(true);
    } catch (error) {
      console.error("Failed to start audio recording:", error);
      setError("Failed to access microphone");
      setTimeout(() => setError(null), 3000);

      // Fallback to browser speech recognition
      setUseAITranscription(false);
      // Use setTimeout to avoid circular dependency
      setTimeout(() => {
        resetTranscript();
        SpeechRecognition.startListening({
          language: getRecognitionLanguage(language),
          continuous: false,
          interimResults: false,
        });
      }, 100);
    }
  }, [sendAudioForTranscription, language, resetTranscript]);

  // Stop audio recording
  const stopAudioRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  }, [isRecording]);

  // Browser speech recognition fallback
  const startBrowserSpeechRecognition = useCallback(async () => {
    try {
      resetTranscript();

      // Check microphone permission
      if (!isMicrophoneAvailable) {
        try {
          await navigator.mediaDevices.getUserMedia({ audio: true });
        } catch (permissionError) {
          console.error("Microphone permission denied:", permissionError);
          setError("Microphone access denied");
          setTimeout(() => setError(null), 3000);
          return;
        }
      }

      SpeechRecognition.startListening({
        language: getRecognitionLanguage(language),
        continuous: false, // Stop after user finishes speaking
        interimResults: false, // Better for mobile performance
      });
    } catch (err) {
      console.error("Failed to start speech recognition:", err);
      setError("Failed to start recording");
      setTimeout(() => setError(null), 3000);
    }
  }, [language, resetTranscript, isMicrophoneAvailable]);

  // Main toggle function
  const toggleRecording = useCallback(() => {
    if (isRecording || listening) {
      // Stop recording
      if (isRecording) {
        stopAudioRecording();
      }
      if (listening) {
        SpeechRecognition.stopListening();
      }
    } else {
      // Start recording - try AI transcription first, fallback to browser
      if (
        typeof window !== "undefined" &&
        typeof window.MediaRecorder !== "undefined" &&
        navigator.mediaDevices &&
        typeof navigator.mediaDevices.getUserMedia === "function"
      ) {
        startAudioRecording();
      } else {
        // Fallback to browser speech recognition
        setUseAITranscription(false);
        startBrowserSpeechRecognition();
      }
    }
  }, [
    isRecording,
    listening,
    startAudioRecording,
    stopAudioRecording,
    startBrowserSpeechRecognition,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  // Don't render if not supported
  if (!browserSupportsSpeechRecognition && !window.MediaRecorder) {
    return null;
  }

  const isActive = isRecording || listening;

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleRecording}
        disabled={disabled || isProcessing}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className={`h-12 w-12 md:h-14 md:w-14 rounded-2xl shadow-lg transition-all duration-200 flex items-center justify-center touch-manipulation ${
          disabled || isProcessing
            ? "opacity-50 cursor-not-allowed bg-gray-100"
            : isActive
            ? "bg-red-50 border-2 border-red-200 hover:bg-red-100 active:bg-red-200 animate-pulse"
            : "bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 active:bg-gray-200"
        }`}
        type="button"
        aria-label={isActive ? "Stop recording" : "Start voice recording"}
        style={{
          WebkitTapHighlightColor: "transparent",
        }}
      >
        {isProcessing ? (
          <Icon
            icon="eos-icons:loading"
            className="h-6 w-6 md:h-7 md:w-7 text-blue-500"
          />
        ) : (
          <Icon
            icon={isActive ? "material-symbols:stop" : "material-symbols:mic"}
            className={`h-6 w-6 md:h-7 md:w-7 ${
              isActive ? "text-red-500" : "text-gray-600"
            }`}
          />
        )}
      </button>

      {/* Tooltip */}
      {showTooltip && !disabled && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-sm rounded-lg whitespace-nowrap z-10">
          {isProcessing
            ? "Processing..."
            : isActive
            ? "Stop recording"
            : "Voice input"}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-red-100 text-red-700 text-sm rounded-lg whitespace-nowrap z-10 max-w-xs text-center">
          {error}
        </div>
      )}

      {/* Recording indicator */}
      {isActive && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full"></div>
        </div>
      )}
    </div>
  );
};

export default EnhancedVoiceRecorder;
