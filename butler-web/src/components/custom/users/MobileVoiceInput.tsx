"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import { Mic, Square, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

interface MobileVoiceInputProps {
  onTranscription: (text: string) => void;
  language?: string;
  disabled?: boolean;
  className?: string;
}

interface RecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  error: string | null;
  duration: number;
}

const MobileVoiceInput: React.FC<MobileVoiceInputProps> = ({
  onTranscription,
  language = "en-US",
  disabled = false,
  className = "",
}) => {
  const [state, setState] = useState<RecordingState>({
    isRecording: false,
    isProcessing: false,
    error: null,
    duration: 0,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    if (mediaRecorderRef.current) {
      mediaRecorderRef.current = null;
    }

    audioChunksRef.current = [];
  }, []);

  // Start recording
  const startRecording = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, error: null, duration: 0 }));

      // Request microphone permission with mobile-optimized constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1,
        },
      });

      streamRef.current = stream;
      audioChunksRef.current = [];

      // Create MediaRecorder with mobile-compatible format
      const mimeType = MediaRecorder.isTypeSupported("audio/webm;codecs=opus")
        ? "audio/webm;codecs=opus"
        : MediaRecorder.isTypeSupported("audio/mp4")
        ? "audio/mp4"
        : "audio/webm";

      const mediaRecorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setState((prev) => ({
          ...prev,
          isRecording: false,
          isProcessing: true,
        }));
        await processAudio();
      };

      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        setState((prev) => ({
          ...prev,
          isRecording: false,
          error: "Recording failed",
        }));
        cleanup();
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      setState((prev) => ({ ...prev, isRecording: true }));

      // Start duration counter
      durationIntervalRef.current = setInterval(() => {
        setState((prev) => ({ ...prev, duration: prev.duration + 1 }));
      }, 1000);

      // Auto-stop after 30 seconds to prevent long recordings
      setTimeout(() => {
        if (mediaRecorderRef.current?.state === "recording") {
          stopRecording();
        }
      }, 30000);
    } catch (error) {
      console.error("Failed to start recording:", error);
      let errorMessage = "Failed to access microphone";

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          errorMessage = "Microphone permission denied";
        } else if (error.name === "NotFoundError") {
          errorMessage = "No microphone found";
        } else if (error.name === "NotSupportedError") {
          errorMessage = "Recording not supported";
        }
      }

      setState((prev) => ({ ...prev, error: errorMessage }));
      toast.error(errorMessage);
      cleanup();
    }
  }, []);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current?.state === "recording") {
      mediaRecorderRef.current.stop();
    }

    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
  }, []);

  // Process recorded audio
  const processAudio = useCallback(async () => {
    try {
      if (audioChunksRef.current.length === 0) {
        throw new Error("No audio data recorded");
      }

      // Create audio blob
      const audioBlob = new Blob(audioChunksRef.current, {
        type: mediaRecorderRef.current?.mimeType || "audio/webm",
      });

      // Check if audio is too short (less than 1 second)
      if (audioBlob.size < 1000) {
        throw new Error("Recording too short");
      }

      // Create form data for API call
      const formData = new FormData();
      formData.append("audio", audioBlob, "recording.webm");
      formData.append("language", language);

      // Send to transcription API
      const response = await fetch("/api/transcribe-audio", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success && result.transcription) {
        onTranscription(result.transcription.trim());
        toast.success("Voice message transcribed!");
      } else {
        throw new Error(result.error || "Transcription failed");
      }
    } catch (error) {
      console.error("Audio processing error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Processing failed";
      setState((prev) => ({ ...prev, error: errorMessage }));
      toast.error(`Transcription failed: ${errorMessage}`);
    } finally {
      setState((prev) => ({ ...prev, isProcessing: false }));
      cleanup();
    }
  }, [language, onTranscription, cleanup]);

  // Toggle recording
  const toggleRecording = useCallback(() => {
    if (state.isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [state.isRecording, startRecording, stopRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Format duration
  // const formatDuration = (seconds: number) => {
  //   const mins = Math.floor(seconds / 60);
  //   const secs = seconds % 60;
  //   return `${mins}:${secs.toString().padStart(2, "0")}`;
  // };

  const isActive = state.isRecording || state.isProcessing;
  const showError = state.error && !isActive;

  return (
    <div className={`flex flex-col items-center gap-2 ${className}`}>
      <Button
        onClick={toggleRecording}
        disabled={disabled || state.isProcessing}
        variant={state.isRecording ? "destructive" : "outline"}
        size="lg"
        className={`
          relative h-12 w-12 rounded-full p-0 transition-all duration-200
          ${
            state.isRecording ? "animate-pulse bg-red-500 hover:bg-red-600" : ""
          }
          ${state.isProcessing ? "cursor-not-allowed opacity-50" : ""}
        `}
      >
        {state.isProcessing ? (
          <Loader2 className="h-5 w-5 animate-spin" />
        ) : state.isRecording ? (
          <Square className="h-5 w-5 fill-current" />
        ) : (
          <Mic className="h-5 w-5" />
        )}
      </Button>

      {/* Status indicators */}
      {state.isRecording && <div className="text-center"></div>}

      {showError && (
        <div className="text-center">
          <div className="text-sm font-medium text-red-600">{state.error}</div>
          <div className="text-xs text-gray-500">Tap to try again</div>
        </div>
      )}

      {!isActive && !showError && (
        <div className="text-center">
          <div className="text-xs text-gray-500"></div>
        </div>
      )}
    </div>
  );
};

export default MobileVoiceInput;
